{"emulators": {"dataconnect": {"dataDir": "dataconnect/.dataconnect/pgliteData", "port": 9399}, "auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "database": {"port": 9000}, "hosting": {"port": 5000}, "pubsub": {"port": 8085}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}, "dataconnect": {"source": "dataconnect"}, "firestore": {"database": "(default)", "location": "me-central1", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "storage": {"rules": "storage.rules"}, "remoteconfig": {"template": "remoteconfig.template.json"}, "extensions": {}, "database": {"rules": "database.rules.json"}}